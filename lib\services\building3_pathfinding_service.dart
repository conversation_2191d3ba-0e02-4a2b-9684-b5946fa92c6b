import '../models/building3_location.dart';

enum NavigationDirection {
  forward,
  backward,
  reverse,
  upFloor,
  downFloor,
  turnLeft,
  turnRight,
  arrived,
}

class NavigationStep {
  final Building3Location from;
  final Building3Location to;
  final NavigationDirection direction;
  final String instruction;
  final int stepNumber;

  NavigationStep({
    required this.from,
    required this.to,
    required this.direction,
    required this.instruction,
    required this.stepNumber,
  });

  @override
  String toString() => instruction;
}

class NavigationRoute {
  final List<NavigationStep> steps;
  final Building3Location destination;
  final int totalSteps;

  NavigationRoute({
    required this.steps,
    required this.destination,
  }) : totalSteps = steps.length;

  bool get isEmpty => steps.isEmpty;
  bool get isNotEmpty => steps.isNotEmpty;

  NavigationStep? getCurrentStep(Building3Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.cite == currentLocation.cite) {
        return steps[i];
      }
    }
    return null;
  }

  NavigationStep? getNextStep(Building3Location currentLocation) {
    for (int i = 0; i < steps.length - 1; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.cite == currentLocation.cite) {
        return steps[i + 1];
      }
    }
    return null;
  }

  int getProgressPercentage(Building3Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.cite == currentLocation.cite) {
        return ((i / totalSteps) * 100).round();
      }
    }
    return 100; // Arrived
  }
}

class Building3PathfindingService {
  static const int _moveCost = 1;
  static const int _floorChangeCost = 2;
  static const int _reverseCost = 1;

  static NavigationRoute? findShortestPath(
    Building3Location start,
    Building3Location destination,
  ) {
    if (start == destination) {
      return NavigationRoute(steps: [], destination: destination);
    }

    // Use Dijkstra's algorithm for pathfinding
    final distances = <String, int>{};
    final previous = <String, Building3Location?>{};
    final unvisited = <Building3Location>[];

    // Initialize distances
    for (final location in Building3LocationData.allLocations) {
      distances[location.id] = double.maxFinite.toInt();
      previous[location.id] = null;
      unvisited.add(location);
    }
    distances[start.id] = 0;

    while (unvisited.isNotEmpty) {
      // Find unvisited node with minimum distance
      unvisited.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));
      final current = unvisited.removeAt(0);

      if (distances[current.id] == double.maxFinite.toInt()) break;
      if (current == destination) break;

      // Check all neighbors
      final neighbors = _getNeighbors(current);
      for (final neighbor in neighbors) {
        if (!unvisited.contains(neighbor)) continue;

        final cost = _calculateMoveCost(current, neighbor);
        final newDistance = distances[current.id]! + cost;

        if (newDistance < distances[neighbor.id]!) {
          distances[neighbor.id] = newDistance;
          previous[neighbor.id] = current;
        }
      }
    }

    // Reconstruct path
    final path = <Building3Location>[];
    Building3Location? current = destination;

    while (current != null) {
      path.insert(0, current);
      current = previous[current.id];
    }

    if (path.isEmpty || path.first != start) {
      return null; // No path found
    }

    // Convert path to navigation steps
    final steps = <NavigationStep>[];
    for (int i = 0; i < path.length - 1; i++) {
      final from = path[i];
      final to = path[i + 1];
      final previousLocation = i > 0 ? path[i - 1] : null;
      final direction = _getDirectionWithContext(from, to, previousLocation);
      final instruction = _getInstruction(from, to, direction);

      steps.add(NavigationStep(
        from: from,
        to: to,
        direction: direction,
        instruction: instruction,
        stepNumber: i + 1,
      ));
    }

    return NavigationRoute(steps: steps, destination: destination);
  }

  static List<Building3Location> _getNeighbors(Building3Location location) {
    final neighbors = <Building3Location>[];

    // Same floor movement
    if (location.cite > 0) {
      // Can move backward (cite - 1)
      final backward = Building3LocationData.findLocationByPosition(
          location.floor, location.cite - 1);
      if (backward != null) neighbors.add(backward);
    }

    if (location.cite < 4) {
      // Can move forward (cite + 1)
      final forward = Building3LocationData.findLocationByPosition(
          location.floor, location.cite + 1);
      if (forward != null) neighbors.add(forward);
    }

    // Floor changes (only from cite 0 or cite 4)
    if (location.cite == 0 || location.cite == 4) {
      // Can go up
      if (location.floor < 4) {
        final upFloor = Building3LocationData.findLocationByPosition(
            location.floor + 1, location.cite);
        if (upFloor != null) neighbors.add(upFloor);
      }

      // Can go down
      if (location.floor > 1) {
        final downFloor = Building3LocationData.findLocationByPosition(
            location.floor - 1, location.cite);
        if (downFloor != null) neighbors.add(downFloor);
      }
    }

    return neighbors;
  }

  static int _calculateMoveCost(Building3Location from, Building3Location to) {
    if (from.floor != to.floor) {
      return _floorChangeCost;
    } else if (from.cite != to.cite) {
      return _moveCost;
    } else {
      return _reverseCost;
    }
  }

  static NavigationDirection _getDirection(
      Building3Location from, Building3Location to) {
    if (from.floor != to.floor) {
      return to.floor > from.floor
          ? NavigationDirection.upFloor
          : NavigationDirection.downFloor;
    } else if (to.cite > from.cite) {
      return NavigationDirection.forward;
    } else if (to.cite < from.cite) {
      return NavigationDirection.backward;
    } else {
      return NavigationDirection.reverse;
    }
  }

  static NavigationDirection _getDirectionWithContext(Building3Location from,
      Building3Location to, Building3Location? previousLocation) {
    // If this is a floor change, return the floor change direction
    if (from.floor != to.floor) {
      return to.floor > from.floor
          ? NavigationDirection.upFloor
          : NavigationDirection.downFloor;
    }

    // If the previous step was a floor change and we're not at cite0,
    // the user is now in reversed view after the floor change
    bool isInReversedView = false;
    if (previousLocation != null &&
        previousLocation.floor != from.floor &&
        from.cite != 0) {
      isInReversedView = true;
    }

    if (isInReversedView) {
      // In reversed view, if we need to move to a higher cite number,
      // we actually move "forward" in the reversed view (which appears as backward)
      if (to.cite > from.cite) {
        return NavigationDirection.forward; // Moving forward in reversed view
      } else if (to.cite < from.cite) {
        return NavigationDirection.backward; // Moving backward in reversed view
      }
    } else {
      // Normal view
      if (to.cite > from.cite) {
        return NavigationDirection.forward;
      } else if (to.cite < from.cite) {
        return NavigationDirection.backward;
      }
    }

    return NavigationDirection.reverse;
  }

  static String _getInstruction(Building3Location from, Building3Location to,
      NavigationDirection direction) {
    switch (direction) {
      case NavigationDirection.forward:
        return 'Move forward to ${to.displayName}';
      case NavigationDirection.backward:
        return 'Move backward to ${to.displayName}';
      case NavigationDirection.reverse:
        return 'Turn around to face ${to.displayName}';
      case NavigationDirection.upFloor:
        return 'Go up to Floor ${to.floor}';
      case NavigationDirection.downFloor:
        return 'Go down to Floor ${to.floor}';
      case NavigationDirection.turnLeft:
        return 'Turn left to ${to.displayName}';
      case NavigationDirection.turnRight:
        return 'Turn right to ${to.displayName}';
      case NavigationDirection.arrived:
        return 'You have arrived at ${to.displayName}';
    }
  }

  static String getDetailedInstruction(NavigationStep step) {
    final direction = step.direction;
    final to = step.to;

    switch (direction) {
      case NavigationDirection.forward:
        return 'Walk straight ahead towards ${to.displayName}';
      case NavigationDirection.backward:
        return 'Walk towards ${to.displayName}';
      case NavigationDirection.upFloor:
        return 'Take the stairs up to Floor ${to.floor}';
      case NavigationDirection.downFloor:
        return 'Take the stairs down to Floor ${to.floor}';
      case NavigationDirection.reverse:
        return 'Turn around to face ${to.displayName}';
      default:
        return step.instruction;
    }
  }
}
