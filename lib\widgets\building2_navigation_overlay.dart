import 'package:flutter/material.dart';
import '../models/building2_location.dart';
import '../services/building2_pathfinding_service.dart';

class Building2NavigationOverlay extends StatefulWidget {
  final Building2NavigationRoute route;
  final Building2Location currentLocation;
  final VoidCallback onStopNavigation;
  final Function(Building2NavigationStep) onStepCompleted;

  const Building2NavigationOverlay({
    Key? key,
    required this.route,
    required this.currentLocation,
    required this.onStopNavigation,
    required this.onStepCompleted,
  }) : super(key: key);

  @override
  _Building2NavigationOverlayState createState() => _Building2NavigationOverlayState();
}

class _Building2NavigationOverlayState extends State<Building2NavigationOverlay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _slideController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Building2NavigationStep? get currentStep {
    return widget.route.getCurrentStep(widget.currentLocation);
  }

  Building2NavigationStep? get nextStep {
    return widget.route.getNextStep(widget.currentLocation);
  }

  bool get hasArrived {
    return widget.currentLocation.floor == widget.route.destination.floor &&
           widget.currentLocation.clage == widget.route.destination.clage;
  }

  int get progressPercentage {
    return widget.route.getProgressPercentage(widget.currentLocation);
  }

  @override
  Widget build(BuildContext context) {
    if (hasArrived) {
      return _buildArrivedOverlay();
    }

    final step = currentStep;
    if (step == null) {
      return _buildNoDirectionOverlay();
    }

    return SlideTransition(
      position: _slideAnimation,
      child: _buildNavigationOverlay(step),
    );
  }

  Widget _buildNavigationOverlay(Building2NavigationStep step) {
    return Positioned(
      top: 80,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.9),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with destination and progress
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Icon(
                        _getDirectionIcon(step.direction),
                        color: Colors.green,
                        size: 24,
                      ),
                    );
                  },
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'To ${widget.route.destination.displayName}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Step ${step.stepNumber} of ${widget.route.totalSteps}',
                        style: TextStyle(
                          color: Colors.grey[300],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: widget.onStopNavigation,
                ),
              ],
            ),
            SizedBox(height: 12),

            // Progress bar
            LinearProgressIndicator(
              value: progressPercentage / 100,
              backgroundColor: Colors.grey[700],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            SizedBox(height: 12),

            // Main instruction
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.5)),
              ),
              child: Row(
                children: [
                  Icon(
                    _getDirectionIcon(step.direction),
                    color: Colors.green,
                    size: 32,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      Building2PathfindingService.getDetailedInstruction(step),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Next step preview
            if (nextStep != null) ...[
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.next_plan,
                      color: Colors.grey[400],
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Next: ${nextStep!.instruction}',
                        style: TextStyle(
                          color: Colors.grey[300],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildArrivedOverlay() {
    return Positioned(
      top: 80,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.95),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 48,
            ),
            SizedBox(height: 12),
            Text(
              'You have arrived!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              widget.route.destination.displayName,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: widget.onStopNavigation,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Done',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDirectionOverlay() {
    return Positioned(
      top: 80,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.9),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Icon(Icons.warning, color: Colors.white),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Navigation guidance not available for current position',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              icon: Icon(Icons.close, color: Colors.white),
              onPressed: widget.onStopNavigation,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDirectionIcon(Building2NavigationDirection direction) {
    switch (direction) {
      case Building2NavigationDirection.forward:
        return Icons.keyboard_arrow_up;
      case Building2NavigationDirection.backward:
        return Icons.keyboard_arrow_down;
      case Building2NavigationDirection.reverse:
        return Icons.autorenew;
      case Building2NavigationDirection.upFloor:
        return Icons.stairs;
      case Building2NavigationDirection.downFloor:
        return Icons.stairs;
      case Building2NavigationDirection.turnLeft:
        return Icons.turn_left;
      case Building2NavigationDirection.turnRight:
        return Icons.turn_right;
      case Building2NavigationDirection.arrived:
        return Icons.check_circle;
    }
  }
}
