import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:test_1_copy_auth_events/pages/building1_navigation.dart';
import '../services/image_cache_service.dart';
import '../models/building2_location.dart';
import '../services/building2_pathfinding_service.dart';
import '../widgets/building2_destination_search_dialog.dart';
import '../widgets/building2_navigation_overlay.dart';

class Building2Navigation extends StatefulWidget {
  final bool isGuest;
  final VoidCallback onExit;
  final bool shouldResetState;

  const Building2Navigation({
    Key? key,
    required this.isGuest,
    required this.onExit,
    required this.shouldResetState,
  }) : super(key: key);

  @override
  _Building2NavigationState createState() => _Building2NavigationState();
}

class _Building2NavigationState extends State<Building2Navigation> {
  int _currentClage = 0;
  bool _isReversed = false;
  int _currentFloor = 1; // Added floor tracking
  bool _isLoading = true;
  String? _currentMapImage;
  final ImageCacheService _imageCacheService = ImageCacheService();
  BuildContext? _safeContext;

  // Navigation state
  Building2NavigationRoute? _activeRoute;
  bool _isNavigating = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store a reference to the context that can be safely used later
    _safeContext = context;
  }

  @override
  void initState() {
    super.initState();
    _initializeState();

    // Preload images using the cache service after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Check if widget is still mounted
        _imageCacheService.preloadBuildingImages(context, 2);
      }
    });
  }

  Future<void> _initializeState() async {
    if (widget.shouldResetState) {
      setState(() {
        _currentClage = 0;
        _isReversed = false;
        _currentMapImage = _getMapImage();
        _isLoading = false;
      });
      return;
    }

    await _loadNavigationState();
  }

  Future<void> _loadNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    final loadedCite = prefs.getInt('${stateKey}_building2_current_cite') ?? 0;
    final loadedReversed =
        prefs.getBool('${stateKey}_building2_is_reversed') ?? false;
    final loadedFloor =
        prefs.getInt('${stateKey}_building2_current_floor') ?? 1;

    setState(() {
      _currentClage = loadedCite;
      _isReversed = loadedReversed;
      _currentFloor = loadedFloor;
      _currentMapImage = _getMapImage();
      _isLoading = false;
    });
  }

  Future<void> _saveNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.setInt('${stateKey}_building2_current_cite', _currentClage);
    await prefs.setBool('${stateKey}_building2_is_reversed', _isReversed);
    await prefs.setInt('${stateKey}_building2_current_floor', _currentFloor);
  }

  Future<void> _clearNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.remove('${stateKey}_building2_current_cite');
    await prefs.remove('${stateKey}_building2_is_reversed');
    await prefs.remove('${stateKey}_building2_current_floor');
  }

  String get _locationName {
    switch (_currentFloor) {
      case 1:
        switch (_currentClage) {
          case 0:
            return 'Main Entrance';
          case 1:
            return 'CAS 101';
          case 2:
            return 'Hyrdaulics Laboratory';
          case 3:
            return 'CAS 103';
          case 4:
            return 'Office of the Dean - CEDE';
          case 5:
            return 'EE Laboratory';
          case 6:
            return 'Office of the Vice President for Academic Affairs';
          case 7:
            return 'International Student Lounge';
          case 8:
            return 'Office of the Dean School of Graduate Studies';
          case 9:
            return 'CAS 106';
          case 10:
            return 'College of Law';
          default:
            return 'Unknown Location';
        }
      case 2:
        switch (_currentClage) {
          case 1:
            return 'Human Resources Department';
          case 2:
            return 'CAS 202';
          case 3:
            return 'CEDE Faculty Room';
          case 4:
            return 'Drawing Room';
          case 5:
            return 'Conference Room';
          case 6:
            return 'Psychology Laboratory';
          case 7:
            return 'Office of the Dean - CLAGE';
          case 8:
            return 'CAS 205';
          case 9:
            return 'CAS 206';
          case 10:
            return 'CAS 206';
          default:
            return 'Unknown Location';
        }
      case 3:
        switch (_currentClage) {
          case 1:
            return 'Chemistry Laboratory 1';
          case 2:
            return 'Chemistry Laboratory 2';
          case 3:
            return 'Center for Science Laboratories and Services';
          case 4:
            return 'Supply Room';
          case 5:
            return 'CAS 305';
          case 6:
            return 'Medtech Laboratory';
          case 7:
            return 'CAS 306';
          case 8:
            return 'CAS 307';
          case 9:
            return 'CAS 308';
          default:
            return 'Unknown Location';
        }
      case 4:
        switch (_currentClage) {
          case 1:
            return 'CAS 401';
          case 2:
            return 'CAS 402';
          case 3:
            return 'CAS 403';
          case 4:
            return 'Office of the Dean - CBAA';
          case 5:
            return 'Consultation Room';
          case 6:
            return 'Consultation Room';
          case 7:
            return 'CAS 404';
          case 8:
            return 'CAS 405';
          case 9:
            return 'CAS 406';
          default:
            return 'Unknown Location';
        }
      default:
        return 'Unknown Location';
    }
  }

  String _getFloorName() {
    switch (_currentFloor) {
      case 1:
        return 'First';
      case 2:
        return 'Second';
      case 3:
        return 'Third';
      case 4:
        return 'Fourth';
      default:
        return 'Unknown';
    }
  }

  String _getMapImage() {
    String floorPrefix = _currentFloor > 1 ? _currentFloor.toString() : '';
    if (_currentClage == 0) {
      return 'assets/CLAGE/${floorPrefix}clage0.jpg';
    }
    return 'assets/CLAGE/${floorPrefix}clage${_currentClage}${_isReversed ? '_r' : ''}.jpg';
  }

  int _getMaxClageForFloor(int floor) {
    switch (floor) {
      case 1:
      case 2:
        return 10; // 1st and 2nd floors go up to clage10
      case 3:
      case 4:
        return 9; // 3rd and 4th floors go up to clage9
      default:
        return 10;
    }
  }

  void _moveForward() {
    setState(() {
      if (_isReversed) {
        // Disable forward on 2clage1_r
        if (_currentClage > 1 && !(_currentFloor == 2 && _currentClage == 1)) {
          _currentClage--;
        } else if (_currentClage == 1 && _currentFloor != 2) {
          _isReversed = false;
          _currentClage = 0;
        }
      } else {
        int maxClage = _getMaxClageForFloor(_currentFloor);
        if (_currentClage < maxClage) {
          _currentClage++;
        }
      }
      _currentMapImage = _getMapImage();
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _moveBackward() {
    setState(() {
      if (_isReversed) {
        int maxClage = _getMaxClageForFloor(_currentFloor);
        if (_currentClage < maxClage) {
          _currentClage++;
        }
      } else {
        if (_currentClage > 1) {
          _currentClage--;
        } else if (_currentClage == 1) {
          _currentClage = 0;
          _isReversed = false;
        }
      }
      _currentMapImage = _getMapImage();
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _toggleReverse() {
    setState(() {
      _isReversed = !_isReversed;
      _currentMapImage = _getMapImage();
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _moveUpOneFloor() {
    if (_currentFloor < 4) {
      setState(() {
        _currentFloor++;
        // Special cases for transitions that should not be reversed
        if ((_currentFloor == 3 &&
                _currentClage == 1) || // 2clage1_r to 3clage1
            (_currentFloor == 4 && _currentClage == 1)) {
          // 3clage1_r to 4clage1
          _isReversed = false; // Force not reversed
        } else if (_currentFloor == 3 && _currentClage == 10) {
          _currentClage = 9; // 2clage10 to 3clage9_r
          _isReversed = true;
        } else if (_currentFloor == 4 && _currentClage == 9) {
          _currentClage = 9; // 3clage9 to 4clage9_r
          _isReversed = true;
        } else {
          _isReversed = true; // Normal case for other floor transitions
        }
        _currentMapImage = _getMapImage();
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  void _moveDownOneFloor() {
    if (_currentFloor > 1) {
      setState(() {
        _currentFloor--;
        if (_currentFloor == 2 && _currentClage == 9) {
          _currentClage = 10; // When going from 3clage9 to 2clage10_r
        }
        _isReversed = true; // Always set to reversed when going down
        _currentMapImage = _getMapImage();
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  bool _isMiddleStairsPosition() {
    return (_isReversed &&
            ((_currentFloor == 1 && _currentClage == 6) ||
                (_currentFloor == 2 && _currentClage == 6) ||
                (_currentFloor == 3 && _currentClage == 5) ||
                (_currentFloor == 4 && _currentClage == 5))) ||
        (!_isReversed &&
            ((_currentFloor == 1 && _currentClage == 5) ||
                (_currentFloor == 2 && _currentClage == 5) ||
                (_currentFloor == 3 && _currentClage == 4) ||
                (_currentFloor == 4 && _currentClage == 4)));
  }

  void _moveUpMiddleStairs() {
    if (_currentFloor < 4) {
      setState(() {
        _currentFloor++;

        // When going up middle stairs from normal view
        if (!_isReversed) {
          // Keep normal view but adjust clage number based on floor
          if (_currentFloor == 2) {
            _currentClage = 6; // From clage5 to 2clage6
          } else if (_currentFloor == 3) {
            _currentClage = 5; // From 2clage5 to 3clage5
          } else if (_currentFloor == 4) {
            _currentClage = 5; // From 3clage4 to 4clage5
          }
        } else {
          // Keep the same orientation but adjust clage number based on floor
          _currentClage = _currentFloor >= 3 ? 5 : 6;
        }

        _currentMapImage = _getMapImage();
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  void _moveDownMiddleStairs() {
    if (_currentFloor > 1) {
      setState(() {
        _currentFloor--;

        // When going down middle stairs
        if (_isReversed) {
          // If we're in reverse view, switch to normal view with correct clage
          _isReversed = false;
          if (_currentFloor == 1) {
            _currentClage = 6; // From 2clage6_r to clage6
          } else if (_currentFloor == 2) {
            _currentClage = 6; // From 3clage5_r to 2clage6
          } else if (_currentFloor == 3) {
            _currentClage = 5; // From 4clage5_r to 3clage5
          }
        } else {
          // Normal view transitions
          _isReversed = false; // Ensure we stay in normal view
          if (_currentFloor == 3) {
            _currentClage = 5; // From 4clage4 to 3clage5
          } else if (_currentFloor == 2) {
            _currentClage = 6; // From 3clage4 to 2clage6
          } else if (_currentFloor == 1) {
            _currentClage = 6; // From 2clage5 to clage6
          }
        }

        _currentMapImage = _getMapImage();
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  Widget _buildMiddleStairsButtons() {
    if (!_isMiddleStairsPosition()) {
      return Container();
    }

    List<Widget> buttons = [];

    // Add Up button if appropriate
    if ((_isReversed &&
            ((_currentFloor == 1 && _currentClage == 6) ||
                (_currentFloor == 2 && _currentClage == 6) ||
                (_currentFloor == 3 && _currentClage == 5))) ||
        (!_isReversed &&
            ((_currentFloor == 1 && _currentClage == 5) ||
                (_currentFloor == 2 && _currentClage == 5) ||
                (_currentFloor == 3 && _currentClage == 4)))) {
      buttons.add(
        FloatingActionButton(
          heroTag: 'middleUpBtn',
          onPressed: _moveUpMiddleStairs,
          backgroundColor: Colors.white.withOpacity(0.7),
          child: Icon(Icons.stairs_outlined, color: Colors.black),
        ),
      );
      buttons.add(SizedBox(height: 10));
    }

    // Add Down button if appropriate
    if ((_isReversed &&
            ((_currentFloor == 2 && _currentClage == 6) ||
                (_currentFloor == 3 && _currentClage == 5) ||
                (_currentFloor == 4 && _currentClage == 5))) ||
        (!_isReversed &&
            ((_currentFloor == 2 && _currentClage == 5) ||
                (_currentFloor == 3 && _currentClage == 4) ||
                (_currentFloor == 4 && _currentClage == 4)))) {
      buttons.add(
        FloatingActionButton(
          heroTag: 'middleDownBtn',
          onPressed: _moveDownMiddleStairs,
          backgroundColor: Colors.white.withOpacity(0.7),
          child: Transform.rotate(
            angle: 300.03, // 180 degrees in radians
            child: Icon(Icons.stairs_outlined, color: Colors.black),
          ),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: buttons,
    );
  }

  Future<void> _precacheImages() async {
    // Precache all possible floor images
    for (int floor = 1; floor <= 4; floor++) {
      String floorPrefix = floor > 1 ? floor.toString() : '';
      // Precache main floor image
      await precacheImage(
          AssetImage('assets/CLAGE/${floorPrefix}clage0.jpg'), context);

      // Precache all clage images for this floor
      int maxClage = _getMaxClageForFloor(floor);
      for (int clage = 1; clage <= maxClage; clage++) {
        await precacheImage(
            AssetImage('assets/CLAGE/${floorPrefix}clage${clage}.jpg'),
            context);
        await precacheImage(
            AssetImage('assets/CLAGE/${floorPrefix}clage${clage}_r.jpg'),
            context);
      }
    }
  }

  List<String> _getLocationInfoList() {
    List<String> infoList = [];

    if (_currentFloor == 1) {
      if (_currentClage == 0) {
        infoList.add('Admissions Office');
      }
      if (_currentClage == 5) {
        infoList.add('Faculty Comfort Room');
        infoList.add('Comfort Room');
      }
      if (_currentClage >= 7 && _currentClage <= 8) {
        infoList.add('Faculty Room');
      }
    } else if (_currentFloor == 2) {
      if (_currentClage == 1) {
        infoList.add('ECE Laboratory');
      }
      if (_currentClage == 5) {
        infoList.add('Comfort Room');
      }
      if (_currentClage == 7) {
        infoList.add('Faculty Room CLAGE');
      }
      if (_currentClage == 8) {
        infoList.add('Student Faculty Consultation Room');
        infoList.add('Faculty Room');
      }
    } else if (_currentFloor == 3) {
      if (_currentClage == 1) {
        infoList.add('Physics Laboratory');
        infoList.add('Anatomy Laboratory');
        infoList.add('Biology Laboratory');
      }
      if (_currentClage == 4) {
        infoList.add('Comfort Room');
      }
      if (_currentClage == 7) {
        infoList.add('Lecture Room');
      }
    } else if (_currentFloor == 4) {
      if (_currentClage == 4) {
        infoList.add('CBAA Faculty Room');
        infoList.add('Mini Conference and Consultation Room');
      }
      if (_currentClage == 5) {
        infoList.add('Comfort Room');
      }
    }

    if (infoList.isEmpty) {
      infoList.add('Additional information not available for this location');
    }

    return infoList;
  }

  void _showLocationInfo() {
    List<String> infoList = _getLocationInfoList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              SizedBox(width: 8),
              Text(
                'Location Information',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: infoList.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 24),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(infoList[index]),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  // Navigation methods
  void _showDestinationSearch() {
    final currentLocation = Building2LocationData.findLocationByPosition(
        _currentFloor, _currentClage);
    if (currentLocation == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Building2DestinationSearchDialog(
          currentLocation: currentLocation,
          onRouteSelected: _startNavigation,
        );
      },
    );
  }

  void _startNavigation(Building2NavigationRoute route) {
    setState(() {
      _activeRoute = route;
      _isNavigating = true;
    });
  }

  void _stopNavigation() {
    setState(() {
      _activeRoute = null;
      _isNavigating = false;
    });
  }

  void _onNavigationStepCompleted(Building2NavigationStep step) {
    // This method can be used to track navigation progress
    // For now, we'll just update the UI when the user moves
  }

  Building2Location? get _currentLocation {
    return Building2LocationData.findLocationByPosition(
        _currentFloor, _currentClage);
  }

  void _checkNavigationProgress() {
    if (!_isNavigating || _activeRoute == null || _currentLocation == null) {
      return;
    }

    // Check if user has arrived at destination
    if (_currentLocation!.floor == _activeRoute!.destination.floor &&
        _currentLocation!.clage == _activeRoute!.destination.clage) {
      // User has arrived, stop navigation after a short delay
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _isNavigating) {
          _stopNavigation();
        }
      });
      return;
    }

    // Check if user is following the route correctly
    final currentStep = _activeRoute!.getCurrentStep(_currentLocation!);
    if (currentStep != null) {
      // User is on the correct path, trigger step completion callback
      _onNavigationStepCompleted(currentStep);
    }
  }

  @override
  void didUpdateWidget(Building2Navigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check navigation progress when the widget updates
    if (_isNavigating) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkNavigationProgress();
      });
    }
  }

  void _moveUpTo3Clage1() {
    setState(() {
      _currentFloor = 3;
      _currentClage = 1;
      _isReversed = false; // Set to normal view when going up to 3clage1
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveUpTo4Clage1() {
    setState(() {
      _currentFloor = 4;
      _currentClage = 1;
      _isReversed = false; // Set to normal view when going up to 4clage1
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveDownTo3Clage1() {
    setState(() {
      _currentFloor = 3;
      _currentClage = 1;
      _isReversed = false; // Set to normal view when going down to 3clage1
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveDownTo2Clage1() {
    setState(() {
      _currentFloor = 2;
      _currentClage = 1;
      _isReversed = false; // Set to normal view when going down to 2clage1
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveUpTo3Clage9Reversed() {
    setState(() {
      _currentFloor = 3;
      _currentClage = 9;
      _isReversed = true; // Set to reversed view when going up to 3clage9_r
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveDownTo1Clage10Reversed() {
    setState(() {
      _currentFloor = 1;
      _currentClage = 10;
      _isReversed = true; // Set to reversed view when going down to clage10_r
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveUpTo4Clage9Reversed() {
    setState(() {
      _currentFloor = 4;
      _currentClage = 9;
      _isReversed = true; // Set to reversed view when going up to 4clage9_r
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveDownTo2Clage10() {
    setState(() {
      _currentFloor = 2;
      _currentClage = 10;
      _isReversed = true; // Set to normal view when going down to 2clage10
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveDownTo3Clage9() {
    setState(() {
      _currentFloor = 3;
      _currentClage = 9;
      _isReversed = true; // Set to normal view when going down to 3clage9
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveUpTo2Clage10Reversed() {
    setState(() {
      _currentFloor = 2;
      _currentClage = 10;
      _isReversed = true; // Set to reversed view when going up to 2clage10_r
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _navigateToBuilding1() {
    // Save current state before navigating
    _saveNavigationState();

    // Navigate to Building 1 while maintaining app state
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => Building1Navigation(
          isGuest: widget.isGuest,
          onExit: widget.onExit,
          shouldResetState: false,
        ),
      ),
    );
  }

  bool _shouldShowRightTurnButton() {
    return _isReversed && _currentFloor == 3 && _currentClage == 1;
  }

  void _showBuildingInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Building Information',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Building information is only accessible through Building 1.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            color: Colors.green,
          ),
        ),
      );
    }

    // Calculate device dimensions for optimal caching
    final Size deviceSize = MediaQuery.of(context).size;
    final int cacheWidth = (deviceSize.width * 1.5).toInt();
    final int cacheHeight = (deviceSize.height * 1.5).toInt();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Building 2 Navigation',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.white,
          onPressed: widget.onExit,
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            color: Colors.white,
            onPressed: _showDestinationSearch,
            tooltip: 'Search Destination',
          ),
          IconButton(
            icon: Icon(Icons.info_outline),
            color: Colors.white,
            onPressed: _showLocationInfo,
            tooltip: 'Location Information',
          ),
        ],
      ),
      body: Stack(
        children: [
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            child: Image.asset(
              _currentMapImage!,
              key: ValueKey(_currentMapImage),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.fill,
              cacheWidth: cacheWidth,
              cacheHeight: cacheHeight,
              gaplessPlayback: true,
            ),
          ),

          // Navigation overlay
          if (_isNavigating && _activeRoute != null && _currentLocation != null)
            Building2NavigationOverlay(
              route: _activeRoute!,
              currentLocation: _currentLocation!,
              onStopNavigation: _stopNavigation,
              onStepCompleted: _onNavigationStepCompleted,
            ),

          Positioned(
            top: 16,
            left: 16,
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.7,
                  ),
                  child: Text(
                    _locationName,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ),
                if (_currentFloor == 1 && _currentClage == 10)
                  Container(
                    margin: EdgeInsets.only(left: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.visibility, color: Colors.white),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      spreadRadius: 2,
                                      blurRadius: 5,
                                      offset: Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(20),
                                        topRight: Radius.circular(20),
                                      ),
                                      child: Image.asset(
                                        'assets/IMAGES/claw.jpg',
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Text(
                                        (_currentFloor == 1 &&
                                                _currentClage == 10)
                                            ? 'College of Law'
                                            : '',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.black
                                              : Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.color,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(5.0),
                                      child: TextButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(),
                                        child: Text('Close',
                                            style: TextStyle(
                                                color: Colors.green,
                                                fontSize: 16)),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _isReversed ? Colors.yellow : Colors.grey,
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.autorenew,
                color: _isReversed ? Colors.yellow : Colors.grey,
                size: 24,
              ),
            ),
          ),
          // Special up button for clage10 at bottom right
          if (!_isReversed && _currentFloor == 1 && _currentClage == 10)
            Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FloatingActionButton(
                      heroTag: 'upBtnClage10',
                      onPressed: _moveUpTo2Clage10Reversed,
                      backgroundColor: Colors.white.withOpacity(0.7),
                      child: Icon(Icons.stairs_outlined, color: Colors.black),
                    ),
                    SizedBox(height: 4),
                    Text('Go Up',
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ])),
                  ],
                ),
              ),
            ),
          // Row for other up/down buttons - responsive positioning
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: AnimatedOpacity(
              duration: Duration(milliseconds: 500),
              opacity: 1.0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Left side - Down button
                  if ((!_isReversed &&
                          _currentFloor == 2 &&
                          _currentClage == 10) ||
                      (!_isReversed &&
                          _currentFloor == 3 &&
                          _currentClage == 9) ||
                      (!_isReversed &&
                          _currentFloor == 4 &&
                          _currentClage == 9) ||
                      (_isReversed &&
                          _currentFloor == 3 &&
                          _currentClage == 1) ||
                      (_isReversed && _currentFloor == 4 && _currentClage == 1))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'downBtn',
                          onPressed: () {
                            if (!_isReversed) {
                              if (_currentFloor == 2 && _currentClage == 10) {
                                _moveDownTo1Clage10Reversed();
                              } else if (_currentFloor == 3 &&
                                  _currentClage == 9) {
                                _moveDownTo2Clage10();
                              } else if (_currentFloor == 4 &&
                                  _currentClage == 9) {
                                _moveDownTo3Clage9();
                              }
                            } else {
                              if (_currentFloor == 3 && _currentClage == 1) {
                                _moveDownTo2Clage1();
                              } else if (_currentFloor == 4 &&
                                  _currentClage == 1) {
                                _moveDownTo3Clage1();
                              }
                            }
                          },
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Transform.rotate(
                            angle: 3.14159, // 180 degrees in radians (π)
                            child: Icon(Icons.stairs_outlined,
                                color: Colors.black),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text('Go Down',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),

                  // Spacing between buttons
                  SizedBox(width: 40),

                  // Right side - Up button (except clage10)
                  if (((!_isReversed &&
                          _currentFloor == 2 &&
                          _currentClage == 10) ||
                      (!_isReversed &&
                          _currentFloor == 3 &&
                          _currentClage == 9) ||
                      (_isReversed &&
                          _currentFloor == 2 &&
                          _currentClage == 1) ||
                      (_isReversed &&
                          _currentFloor == 3 &&
                          _currentClage == 1)))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'upBtn',
                          onPressed: () {
                            if (!_isReversed) {
                              if (_currentFloor == 2 && _currentClage == 10) {
                                _moveUpTo3Clage9Reversed();
                              } else if (_currentFloor == 3 &&
                                  _currentClage == 9) {
                                _moveUpTo4Clage9Reversed();
                              }
                            } else {
                              if (_currentFloor == 2 && _currentClage == 1) {
                                _moveUpTo3Clage1();
                              } else if (_currentFloor == 3 &&
                                  _currentClage == 1) {
                                _moveUpTo4Clage1();
                              }
                            }
                          },
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child:
                              Icon(Icons.stairs_outlined, color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Go Up',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),
                ],
              ),
            ),
          ),
          // Vertical navigation buttons on the right
          Positioned(
            bottom: 40,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Forward button (pointing up)
                if ((_currentClage == 0) ||
                    (_currentClage != 0 &&
                        !(_isReversed &&
                            _currentClage == 1 &&
                            (_currentFloor == 2 ||
                                _currentFloor == 3 ||
                                _currentFloor == 4)) &&
                        !(!_isReversed &&
                            ((_currentFloor == 1 && _currentClage == 10) ||
                                (_currentFloor == 2 && _currentClage == 10) ||
                                (_currentFloor == 3 && _currentClage == 9) ||
                                (_currentFloor == 4 && _currentClage == 9)))))
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'forwardBtn',
                        onPressed: _moveForward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child:
                            Icon(Icons.keyboard_arrow_up, color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Forward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),

                SizedBox(height: 10),

                // Reverse button
                if (_currentClage != 0)
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'reverseBtn',
                        onPressed: _toggleReverse,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.autorenew,
                            color: Colors.black, size: 30),
                      ),
                      SizedBox(height: 4),
                      Text('Reverse',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),

                SizedBox(height: 10),

                // Back button (pointing down)
                if (_currentClage != 0 &&
                    !((_currentClage == 1 && !_isReversed) &&
                        (_currentFloor == 2 ||
                            _currentFloor == 3 ||
                            _currentFloor == 4)) &&
                    !(_isReversed &&
                        ((_currentFloor == 1 && _currentClage == 10) ||
                            (_currentFloor == 2 && _currentClage == 10) ||
                            (_currentFloor == 3 && _currentClage == 9) ||
                            (_currentFloor == 4 && _currentClage == 9))))
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'backBtn',
                        onPressed: _moveBackward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.keyboard_arrow_down,
                            color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Backward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),
              ],
            ),
          ),
          // Middle stairs buttons - responsive positioning
          if (_isMiddleStairsPosition() &&
              ((
                      // Up button conditions from _buildMiddleStairsButtons
                      (_isReversed &&
                              ((_currentFloor == 1 && _currentClage == 6) ||
                                  (_currentFloor == 2 && _currentClage == 6) ||
                                  (_currentFloor == 3 &&
                                      _currentClage == 5))) ||
                          (!_isReversed &&
                              ((_currentFloor == 1 && _currentClage == 5) ||
                                  (_currentFloor == 2 && _currentClage == 5) ||
                                  (_currentFloor == 3 &&
                                      _currentClage == 4)))) ||
                  // Down button conditions
                  ((_isReversed &&
                          ((_currentFloor == 2 && _currentClage == 6) ||
                              (_currentFloor == 3 && _currentClage == 5) ||
                              (_currentFloor == 4 && _currentClage == 5))) ||
                      (!_isReversed &&
                          ((_currentFloor == 2 && _currentClage == 5) ||
                              (_currentFloor == 3 && _currentClage == 4) ||
                              (_currentFloor == 4 && _currentClage == 4))))))
            Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Middle stairs down button
                  if (_isMiddleStairsPosition() &&
                      ((_isReversed &&
                              ((_currentFloor == 2 && _currentClage == 6) ||
                                  (_currentFloor == 3 && _currentClage == 5) ||
                                  (_currentFloor == 4 &&
                                      _currentClage == 5))) ||
                          (!_isReversed &&
                              ((_currentFloor == 2 && _currentClage == 5) ||
                                  (_currentFloor == 3 && _currentClage == 4) ||
                                  (_currentFloor == 4 && _currentClage == 4)))))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'middleStairsDownBtn',
                          onPressed: _moveDownMiddleStairs,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Transform.rotate(
                            angle: 3.14159, // 180 degrees in radians (π)
                            child: Icon(Icons.stairs_outlined,
                                color: Colors.black),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text('Go Down',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),

                  // Spacing between buttons
                  SizedBox(width: 40),

                  // Middle stairs up button
                  if (_isMiddleStairsPosition() &&
                      (
                          // Up button conditions from _buildMiddleStairsButtons
                          (_isReversed &&
                                  ((_currentFloor == 1 && _currentClage == 6) ||
                                      (_currentFloor == 2 &&
                                          _currentClage == 6) ||
                                      (_currentFloor == 3 &&
                                          _currentClage == 5))) ||
                              (!_isReversed &&
                                  ((_currentFloor == 1 && _currentClage == 5) ||
                                      (_currentFloor == 2 &&
                                          _currentClage == 5) ||
                                      (_currentFloor == 3 &&
                                          _currentClage == 4)))))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'middleStairsUpBtn',
                          onPressed: _moveUpMiddleStairs,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child:
                              Icon(Icons.stairs_outlined, color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Go Up',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),
                ],
              ),
            ),
          if (_shouldShowRightTurnButton())
            Positioned(
              right: 16,
              bottom: MediaQuery.of(context).size.height * 0.35,
              child: FloatingActionButton(
                heroTag: 'rightTurnBtn',
                onPressed: _showBuildingInfoDialog,
                backgroundColor: Colors.white.withOpacity(0.7),
                child: Icon(Icons.turn_right, color: Colors.black),
              ),
            ),
          // Stairs on the left message for middle stairs (normal view)
          if (!_isReversed &&
              ((_currentFloor == 1 && _currentClage == 5) ||
                  (_currentFloor == 2 && _currentClage == 5) ||
                  (_currentFloor == 3 && _currentClage == 4) ||
                  (_currentFloor == 4 && _currentClage == 4)))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Stairs on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Accounting office on the left message
          if (!_isReversed && (_currentFloor == 1 && _currentClage == 0))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Accounting office on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Accounting office on the right message
          if (_isReversed && (_currentFloor == 1 && _currentClage == 1))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Accounting office on the right',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Admissions office on the right message
          if (_isReversed && (_currentFloor == 1 && _currentClage == 3))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Admissions office on the right',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Admissions office on the left message
          if (!_isReversed && (_currentFloor == 1 && _currentClage == 3))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Admissions office on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // College of law just ahead message
          if (!_isReversed &&
              ((_currentFloor == 1 && _currentClage == 10) ||
                  (_currentFloor == 2 && _currentClage == 10)))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'College of law just ahead',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Stairs just ahead message
          if (_isReversed && (_currentFloor == 4 && _currentClage == 1))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Stairs just ahead',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Comfort room on the left message
          if (_isReversed &&
              ((((_currentFloor == 1 && _currentClage == 6) ||
                  (_currentFloor == 2 && _currentClage == 6) ||
                  (_currentFloor == 3 && _currentClage == 5) ||
                  (_currentFloor == 4 && _currentClage == 5)))))
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Comfort room on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Only save state if we're not resetting
    if (!widget.shouldResetState) {
      _saveNavigationState();
    } else {
      _clearNavigationState();
    }

    // Don't use context in dispose
    _safeContext = null;

    super.dispose();
  }
}
