import 'package:flutter/material.dart';
import '../models/building2_location.dart';
import '../services/building2_pathfinding_service.dart';

class Building2DestinationSearchDialog extends StatefulWidget {
  final Building2Location currentLocation;
  final Function(Building2NavigationRoute) onRouteSelected;

  const Building2DestinationSearchDialog({
    Key? key,
    required this.currentLocation,
    required this.onRouteSelected,
  }) : super(key: key);

  @override
  _Building2DestinationSearchDialogState createState() => _Building2DestinationSearchDialogState();
}

class _Building2DestinationSearchDialogState extends State<Building2DestinationSearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Building2Location> _searchResults = [];
  Building2Location? _selectedDestination;
  Building2NavigationRoute? _previewRoute;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchResults = Building2LocationData.allLocations
        .where((location) => location != widget.currentLocation)
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
      if (query.isEmpty) {
        _searchResults = Building2LocationData.allLocations
            .where((location) => location != widget.currentLocation)
            .toList();
      } else {
        _searchResults = Building2LocationData.searchLocations(query)
            .where((location) => location != widget.currentLocation)
            .toList();
      }
      _selectedDestination = null;
      _previewRoute = null;
    });
  }

  void _onLocationSelected(Building2Location location) {
    setState(() {
      _selectedDestination = location;
      _previewRoute = Building2PathfindingService.findShortestPath(
        widget.currentLocation,
        location,
      );
    });
  }

  void _onFollowPressed() {
    if (_previewRoute != null) {
      widget.onRouteSelected(_previewRoute!);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.search, color: Colors.green, size: 28),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Search Destination',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Current location info
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.my_location, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Current: ${widget.currentLocation.displayName}',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),

            // Search field
            TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search for a destination...',
                prefixIcon: Icon(Icons.search, color: Colors.grey),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.green, width: 2),
                ),
              ),
            ),
            SizedBox(height: 16),

            // Search results
            Expanded(
              child: _buildSearchResults(),
            ),

            // Route preview and follow button
            if (_selectedDestination != null) _buildRoutePreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              _isSearching ? 'No destinations found' : 'No destinations available',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final location = _searchResults[index];
        final isSelected = _selectedDestination == location;
        
        return Card(
          margin: EdgeInsets.symmetric(vertical: 4),
          elevation: isSelected ? 4 : 1,
          color: isSelected ? Colors.green.withOpacity(0.1) : null,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isSelected ? Colors.green : Colors.grey[300],
              child: Text(
                location.floor.toString(),
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              location.displayName,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.green[700] : null,
              ),
            ),
            subtitle: Text(
              'Floor ${location.floor} • Position ${location.clage}',
              style: TextStyle(
                color: isSelected ? Colors.green[600] : Colors.grey[600],
              ),
            ),
            trailing: isSelected
                ? Icon(Icons.check_circle, color: Colors.green)
                : Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _onLocationSelected(location),
          ),
        );
      },
    );
  }

  Widget _buildRoutePreview() {
    return Container(
      margin: EdgeInsets.only(top: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.route, color: Colors.green),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Route to ${_selectedDestination!.displayName}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          if (_previewRoute != null) ...[
            Text(
              '${_previewRoute!.totalSteps} steps',
              style: TextStyle(color: Colors.green[600]),
            ),
            SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _onFollowPressed,
                icon: Icon(Icons.navigation, color: Colors.white),
                label: Text(
                  'Follow Route',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ] else ...[
            Text(
              'No route available',
              style: TextStyle(color: Colors.red[600]),
            ),
          ],
        ],
      ),
    );
  }
}
