import '../models/building2_location.dart';

enum Building2NavigationDirection {
  forward,
  backward,
  reverse,
  upFloor,
  downFloor,
  turnLeft,
  turnRight,
  arrived,
}

class Building2NavigationStep {
  final Building2Location from;
  final Building2Location to;
  final Building2NavigationDirection direction;
  final String instruction;
  final int stepNumber;

  Building2NavigationStep({
    required this.from,
    required this.to,
    required this.direction,
    required this.instruction,
    required this.stepNumber,
  });

  @override
  String toString() => instruction;
}

class Building2NavigationRoute {
  final List<Building2NavigationStep> steps;
  final Building2Location destination;
  final int totalSteps;

  Building2NavigationRoute({
    required this.steps,
    required this.destination,
  }) : totalSteps = steps.length;

  bool get isEmpty => steps.isEmpty;
  bool get isNotEmpty => steps.isNotEmpty;

  Building2NavigationStep? getCurrentStep(Building2Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return steps[i];
      }
    }
    return null;
  }

  Building2NavigationStep? getNextStep(Building2Location currentLocation) {
    for (int i = 0; i < steps.length - 1; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return steps[i + 1];
      }
    }
    return null;
  }

  int getProgressPercentage(Building2Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return ((i / totalSteps) * 100).round();
      }
    }
    return 100; // Arrived
  }
}

class Building2PathfindingService {
  static const int _moveCost = 1;
  static const int _floorChangeCost = 2;
  static const int _reverseCost = 1;

  static Building2NavigationRoute? findShortestPath(
    Building2Location start,
    Building2Location destination, {
    bool isStartReversed = false,
  }) {
    if (start == destination) {
      return Building2NavigationRoute(steps: [], destination: destination);
    }

    // Use Dijkstra's algorithm for pathfinding
    final distances = <String, int>{};
    final previous = <String, Building2Location?>{};
    final unvisited = <Building2Location>[];

    // Initialize distances
    for (final location in Building2LocationData.allLocations) {
      distances[location.id] = double.maxFinite.toInt();
      previous[location.id] = null;
      unvisited.add(location);
    }
    distances[start.id] = 0;

    while (unvisited.isNotEmpty) {
      // Find unvisited node with minimum distance
      unvisited.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));
      final current = unvisited.removeAt(0);

      if (distances[current.id] == double.maxFinite.toInt()) break;
      if (current == destination) break;

      // Check all neighbors
      final neighbors = _getNeighbors(current);
      for (final neighbor in neighbors) {
        if (!unvisited.contains(neighbor)) continue;

        final cost = _calculateMoveCost(current, neighbor);
        final newDistance = distances[current.id]! + cost;

        if (newDistance < distances[neighbor.id]!) {
          distances[neighbor.id] = newDistance;
          previous[neighbor.id] = current;
        }
      }
    }

    // Reconstruct path
    final path = <Building2Location>[];
    Building2Location? current = destination;

    while (current != null) {
      path.insert(0, current);
      current = previous[current.id];
    }

    if (path.isEmpty || path.first != start) {
      return null; // No path found
    }

    // Convert path to navigation steps
    final steps = <Building2NavigationStep>[];
    for (int i = 0; i < path.length - 1; i++) {
      final from = path[i];
      final to = path[i + 1];
      final previousLocation = i > 0 ? path[i - 1] : null;

      // For the first step, consider if the user is starting in reversed mode
      bool isCurrentlyReversed = false;
      if (i == 0) {
        isCurrentlyReversed = isStartReversed;
      } else if (previousLocation != null &&
          previousLocation.floor != from.floor &&
          from.clage != 0) {
        isCurrentlyReversed = true;
      }

      final direction = _getDirectionWithContext(
          from, to, previousLocation, isCurrentlyReversed);
      final instruction = _getInstruction(from, to, direction);

      steps.add(Building2NavigationStep(
        from: from,
        to: to,
        direction: direction,
        instruction: instruction,
        stepNumber: i + 1,
      ));
    }

    return Building2NavigationRoute(steps: steps, destination: destination);
  }

  static List<Building2Location> _getNeighbors(Building2Location location) {
    final neighbors = <Building2Location>[];
    final maxClage = Building2LocationData.getMaxClageForFloor(location.floor);

    // Same floor movement
    if (location.clage > 0) {
      // Can move backward (clage - 1)
      final backward = Building2LocationData.findLocationByPosition(
          location.floor, location.clage - 1);
      if (backward != null) neighbors.add(backward);
    }

    if (location.clage < maxClage) {
      // Can move forward (clage + 1)
      final forward = Building2LocationData.findLocationByPosition(
          location.floor, location.clage + 1);
      if (forward != null) neighbors.add(forward);
    }

    // Floor changes (only from clage 0 or clage maxClage)
    if (location.clage == 0 || location.clage == maxClage) {
      // Can go up
      if (location.floor < 5) {
        final upFloor = Building2LocationData.findLocationByPosition(
            location.floor + 1, location.clage);
        if (upFloor != null) neighbors.add(upFloor);
      }

      // Can go down
      if (location.floor > 1) {
        final downFloor = Building2LocationData.findLocationByPosition(
            location.floor - 1, location.clage);
        if (downFloor != null) neighbors.add(downFloor);
      }
    }

    return neighbors;
  }

  static int _calculateMoveCost(Building2Location from, Building2Location to) {
    if (from.floor != to.floor) {
      return _floorChangeCost;
    } else if (from.clage != to.clage) {
      return _moveCost;
    } else {
      return _reverseCost;
    }
  }

  static Building2NavigationDirection _getDirectionWithContext(
      Building2Location from,
      Building2Location to,
      Building2Location? previousLocation,
      bool isCurrentlyReversed) {
    // If this is a floor change, return the floor change direction
    if (from.floor != to.floor) {
      return to.floor > from.floor
          ? Building2NavigationDirection.upFloor
          : Building2NavigationDirection.downFloor;
    }

    // Determine if user is in reversed view
    bool isInReversedView = isCurrentlyReversed;

    // Also check if the previous step was a floor change and we're not at clage0
    if (!isInReversedView &&
        previousLocation != null &&
        previousLocation.floor != from.floor &&
        from.clage != 0) {
      isInReversedView = true;
    }

    if (isInReversedView) {
      // In reversed view, the movement directions are inverted
      if (to.clage > from.clage) {
        return Building2NavigationDirection
            .backward; // Need to move backward to go to higher clage
      } else if (to.clage < from.clage) {
        return Building2NavigationDirection
            .forward; // Need to move forward to go to lower clage
      }
    } else {
      // Normal view - directions match clage numbers
      if (to.clage > from.clage) {
        return Building2NavigationDirection
            .forward; // Moving to higher clage = forward
      } else if (to.clage < from.clage) {
        return Building2NavigationDirection
            .backward; // Moving to lower clage = backward
      }
    }

    return Building2NavigationDirection.reverse;
  }

  static String _getInstruction(Building2Location from, Building2Location to,
      Building2NavigationDirection direction) {
    switch (direction) {
      case Building2NavigationDirection.forward:
        return 'Move forward to ${to.displayName}';
      case Building2NavigationDirection.backward:
        return 'Move backward to ${to.displayName}';
      case Building2NavigationDirection.reverse:
        return 'Turn around to face ${to.displayName}';
      case Building2NavigationDirection.upFloor:
        return 'Go up to Floor ${to.floor}';
      case Building2NavigationDirection.downFloor:
        return 'Go down to Floor ${to.floor}';
      case Building2NavigationDirection.turnLeft:
        return 'Turn left to ${to.displayName}';
      case Building2NavigationDirection.turnRight:
        return 'Turn right to ${to.displayName}';
      case Building2NavigationDirection.arrived:
        return 'You have arrived at ${to.displayName}';
    }
  }

  static String getDetailedInstruction(Building2NavigationStep step) {
    final direction = step.direction;
    final to = step.to;

    switch (direction) {
      case Building2NavigationDirection.forward:
        return 'Walk forward towards ${to.displayName}';
      case Building2NavigationDirection.backward:
        return 'Walk backward towards ${to.displayName}';
      case Building2NavigationDirection.upFloor:
        return 'Take the stairs up to Floor ${to.floor}';
      case Building2NavigationDirection.downFloor:
        return 'Take the stairs down to Floor ${to.floor}';
      case Building2NavigationDirection.reverse:
        return 'Turn around to face ${to.displayName}';
      default:
        return step.instruction;
    }
  }
}
